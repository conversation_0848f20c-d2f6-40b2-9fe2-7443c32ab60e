{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load form_utils %}
{% load django_permissions %}

{% block title %}{{ title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:list' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
        <h5 class="mb-0">
            {% if form.instance.emp_id %}
            <i class="fas fa-edit me-2"></i>
            {% else %}
            <i class="fas fa-user-plus me-2"></i>
            {% endif %}
            {{ title }}
        </h5>
        <a href="{% url 'Hr:employees:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger" role="alert">
                {% for error in form.non_field_errors %}
                <p class="mb-0">{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}

            <!-- تنظيم الحقول في أقسام -->
            <div class="accordion mb-4" id="employeeFormAccordion">
                {% for section_title, fields in form.fieldsets %}
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading{{ forloop.counter }}">
                        <button class="accordion-button {% if not forloop.first %}collapsed{% endif %}" type="button"
                                data-bs-toggle="collapse" data-bs-target="#collapse{{ forloop.counter }}"
                                aria-expanded="{% if forloop.first %}true{% else %}false{% endif %}"
                                aria-controls="collapse{{ forloop.counter }}">
                            {{ section_title }}
                        </button>
                    </h2>
                    <div id="collapse{{ forloop.counter }}"
                         class="accordion-collapse collapse {% if forloop.first %}show{% endif %}"
                         aria-labelledby="heading{{ forloop.counter }}"
                         data-bs-parent="#employeeFormAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                {% for field_name in fields %}
                                {% with field=form|getattribute:field_name %}
                                {% if field %}
                                <div class="col-md-4 mb-3">
                                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                    {{ field }}
                                    {% if field.help_text %}
                                    <small class="form-text text-muted">{{ field.help_text }}</small>
                                    {% endif %}
                                    {% for error in field.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% else %}
                                <!-- حقل غير موجود: {{ field_name }} -->
                                {% endif %}
                                {% endwith %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{% url 'Hr:employees:list' %}" class="btn btn-light me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary px-4">
                    <i class="fas fa-save me-1"></i>
                    {{ button_text|default:"حفظ التغييرات" }}
                </button>
            </div>
        </form>
    </div>
    <div class="actions text-end">
        {% if perms.Hr.delete_employee or user|is_admin %}
        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteEmployeeModal">
            <i class="fas fa-trash me-1"></i> حذف
        </button>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحقق من صحة النماذج
    (function () {
        'use strict'

        // أشكال تحتاج إلى التحقق من صحتها
        var forms = document.querySelectorAll('.needs-validation')

        // حلقة عليهم ومنع الإرسال
        Array.prototype.slice.call(forms)
            .forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }

                    form.classList.add('was-validated')
                }, false)
            })
    })()

    // تعبئة الاسم الكامل تلقائيًا
    const firstNameInput = document.getElementById('id_emp_first_name');
    const secondNameInput = document.getElementById('id_emp_second_name');
    const fullNameInput = document.getElementById('id_emp_full_name');

    if (firstNameInput && secondNameInput && fullNameInput) {
        const updateFullName = () => {
            const firstName = firstNameInput.value.trim() || '';
            const secondName = secondNameInput.value.trim() || '';

            if (firstName || secondName) {
                fullNameInput.value = [firstName, secondName].filter(Boolean).join(' ');
            }
        };

        firstNameInput.addEventListener('blur', updateFullName);
        secondNameInput.addEventListener('blur', updateFullName);
    }

    // حساب مبلغ التأمين المستحق
    const insuranceSalaryInput = document.getElementById('id_insurance_salary');
    const percentageInput = document.getElementById('id_percentage_insurance_payable');
    const dueAmountInput = document.getElementById('id_due_insurance_amount');

    if (insuranceSalaryInput && percentageInput && dueAmountInput) {
        const updateDueAmount = () => {
            const salary = parseFloat(insuranceSalaryInput.value) || 0;
            const percentage = parseFloat(percentageInput.value) || 0;

            if (salary && percentage) {
                dueAmountInput.value = (salary * percentage / 100).toFixed(2);
            } else {
                dueAmountInput.value = '';
            }
        };

        insuranceSalaryInput.addEventListener('input', updateDueAmount);
        percentageInput.addEventListener('input', updateDueAmount);
    }
});
</script>
{% endblock %}

{% block extra_css %}
<style>
    /* إعدادات التصميم لنموذج الموظف */
    .accordion-button:not(.collapsed) {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }

    .accordion-button:focus {
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
</style>
{% endblock %}
