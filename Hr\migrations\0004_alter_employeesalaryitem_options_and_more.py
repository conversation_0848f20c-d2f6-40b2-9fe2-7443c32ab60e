# Generated by Django 5.0.14 on 2025-06-03 11:16

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Hr', '0003_alter_employee_emp_image'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='employeesalaryitem',
            options={'ordering': ['employee', 'salary_item'], 'verbose_name': 'بند راتب موظف', 'verbose_name_plural': 'بنود رواتب الموظفين'},
        ),
        migrations.AlterModelOptions(
            name='payrollentry',
            options={'ordering': ['-period__period', 'employee'], 'verbose_name': 'سجل راتب', 'verbose_name_plural': 'سجلات الرواتب'},
        ),
        migrations.AlterModelOptions(
            name='payrollitemdetail',
            options={'ordering': ['payroll_entry', 'salary_item'], 'verbose_name': 'تفاصيل بند راتب', 'verbose_name_plural': 'تفاصيل بنود الرواتب'},
        ),
        migrations.AlterModelOptions(
            name='payrollperiod',
            options={'ordering': ['-period'], 'verbose_name': 'فترة راتب', 'verbose_name_plural': 'فترات الرواتب'},
        ),
        migrations.AlterModelOptions(
            name='salaryitem',
            options={'ordering': ['item_code'], 'verbose_name': 'بند راتب', 'verbose_name_plural': 'بنود الرواتب'},
        ),
        migrations.RemoveField(
            model_name='employeesalaryitem',
            name='effective_date',
        ),
        migrations.RemoveField(
            model_name='employeesalaryitem',
            name='value',
        ),
        migrations.AlterUniqueTogether(
            name='payrollentry',
            unique_together={('period', 'employee')},
        ),
        migrations.AlterUniqueTogether(
            name='payrollperiod',
            unique_together={('period',)},
        ),
        migrations.RemoveField(
            model_name='salaryitem',
            name='affects_total',
        ),
        migrations.RemoveField(
            model_name='salaryitem',
            name='calculation_method',
        ),
        migrations.RemoveField(
            model_name='salaryitem',
            name='item_type',
        ),
        migrations.AddField(
            model_name='employeesalaryitem',
            name='amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='القيمة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='employeesalaryitem',
            name='start_date',
            field=models.DateField(default=0, verbose_name='تاريخ البدء'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='officialholiday',
            name='is_recurring',
            field=models.BooleanField(default=False, verbose_name='إجازة متكررة'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='period',
            field=models.ForeignKey(default=0, on_delete=django.db.models.deletion.PROTECT, related_name='entries', to='Hr.payrollperiod', verbose_name='فترة الراتب'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='status',
            field=models.CharField(choices=[('pending', 'قيد المراجعة'), ('approved', 'معتمد'), ('rejected', 'مرفوض'), ('paid', 'مدفوع')], default='pending', max_length=20, verbose_name='الحالة'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='إجمالي المبلغ'),
        ),
        migrations.AddField(
            model_name='payrollitemdetail',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=0, verbose_name='تاريخ الإنشاء'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='approved_payroll_periods', to=settings.AUTH_USER_MODEL, verbose_name='تم الاعتماد بواسطة'),
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='created_by',
            field=models.ForeignKey(default=0, on_delete=django.db.models.deletion.PROTECT, related_name='created_payroll_periods', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='period',
            field=models.DateField(default=0, verbose_name='الفترة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='إجمالي المبلغ'),
        ),
        migrations.AddField(
            model_name='salaryitem',
            name='default_value',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='القيمة الافتراضية'),
        ),
        migrations.AddField(
            model_name='salaryitem',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='نشط'),
        ),
        migrations.AddField(
            model_name='salaryitem',
            name='is_auto_applied',
            field=models.BooleanField(default=False, verbose_name='تطبيق تلقائي'),
        ),
        migrations.AddField(
            model_name='salaryitem',
            name='item_code',
            field=models.CharField(default=0, max_length=20, unique=True, verbose_name='كود البند'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='salaryitem',
            name='type',
            field=models.CharField(choices=[('addition', 'إضافة'), ('deduction', 'خصم')], default='addition', max_length=20, verbose_name='نوع البند'),
        ),
        migrations.AlterField(
            model_name='employeesalaryitem',
            name='salary_item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='Hr.salaryitem', verbose_name='بند الراتب'),
        ),
        migrations.AlterField(
            model_name='payrollentry',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payroll_entries', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AlterField(
            model_name='payrollitemdetail',
            name='amount',
            field=models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='القيمة'),
        ),
        migrations.AlterField(
            model_name='payrollitemdetail',
            name='payroll_entry',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='Hr.payrollentry', verbose_name='سجل الراتب'),
        ),
        migrations.AlterField(
            model_name='payrollitemdetail',
            name='salary_item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='Hr.salaryitem', verbose_name='بند الراتب'),
        ),
        migrations.AlterField(
            model_name='payrollperiod',
            name='status',
            field=models.CharField(choices=[('draft', 'مسودة'), ('calculating', 'جاري الحساب'), ('completed', 'مكتمل'), ('approved', 'معتمد'), ('paid', 'مدفوع')], default='draft', max_length=20, verbose_name='الحالة'),
        ),
        migrations.AlterField(
            model_name='salaryitem',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='الوصف'),
        ),
        migrations.AlterModelTable(
            name='employeesalaryitem',
            table=None,
        ),
        migrations.AlterModelTable(
            name='payrollentry',
            table=None,
        ),
        migrations.AlterModelTable(
            name='payrollitemdetail',
            table=None,
        ),
        migrations.AlterModelTable(
            name='payrollperiod',
            table=None,
        ),
        migrations.AlterModelTable(
            name='salaryitem',
            table=None,
        ),
        migrations.RemoveField(
            model_name='payrollentry',
            name='allowances',
        ),
        migrations.RemoveField(
            model_name='payrollentry',
            name='basic_salary',
        ),
        migrations.RemoveField(
            model_name='payrollentry',
            name='deductions',
        ),
        migrations.RemoveField(
            model_name='payrollentry',
            name='overtime',
        ),
        migrations.RemoveField(
            model_name='payrollentry',
            name='payroll_period',
        ),
        migrations.RemoveField(
            model_name='payrollentry',
            name='penalties',
        ),
        migrations.RemoveField(
            model_name='payrollentry',
            name='total_salary',
        ),
        migrations.RemoveField(
            model_name='payrollentry',
            name='variable_salary',
        ),
        migrations.RemoveField(
            model_name='payrollperiod',
            name='end_date',
        ),
        migrations.RemoveField(
            model_name='payrollperiod',
            name='name',
        ),
        migrations.RemoveField(
            model_name='payrollperiod',
            name='start_date',
        ),
    ]
