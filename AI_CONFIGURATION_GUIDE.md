# 🤖 دليل إعداد الذكاء الاصطناعي - نظام الدولية

## 🎯 نظرة عامة

تم إنشاء نظام شامل لإدارة إعدادات الذكاء الاصطناعي يدعم مقدمي خدمات متعددين مع واجهة سهلة الاستخدام لإدارة مفاتيح API واختبار الاتصالات.

## ✨ الميزات الجديدة

### 🔧 إدارة مقدمي الخدمات
- **دعم متعدد المقدمين**: Gemini, OpenAI, Claude, Hugging Face, <PERSON><PERSON><PERSON>, ومقدمين مخصصين
- **إدارة آمنة للمفاتيح**: تشفير وحفظ آمن لمفاتيح API
- **اختبار الاتصال**: اختبار المفاتيح قبل الحفظ
- **إعدادات متقدمة**: التحكم في عدد الرموز ودرجة الإبداع
- **مقدم افتراضي**: تحديد المقدم الافتراضي للمحادثات

### 📊 واجهات مرئية
- **صفحة إعدادات AI**: عرض جميع المقدمين وحالتهم
- **إضافة إعداد جديد**: واجهة سهلة لإضافة مقدمين
- **تعديل الإعدادات**: تحديث المفاتيح والإعدادات
- **إحصائيات الاستخدام**: متابعة استخدام كل مقدم

## 🚀 التشغيل السريع

### الطريقة الأولى: التشغيل الكامل مع إعداد AI
```bash
start_with_ai_setup.bat
```

### الطريقة الثانية: يدوياً
```bash
# تثبيت المكتبات
pip install djangorestframework drf-yasg djangorestframework-simplejwt django-cors-headers google-generativeai python-dotenv

# الترحيلات
python manage.py makemigrations api
python manage.py migrate

# إعداد مقدمي الخدمات
python manage.py setup_ai_providers

# التشغيل
python manage.py runserver
```

## 🌐 الوصول للنظام

### الصفحات الجديدة:
- **إعدادات AI**: http://localhost:8000/api/v1/ai/settings/
- **إضافة مقدم**: http://localhost:8000/api/v1/ai/add-config/
- **لوحة تحكم API**: http://localhost:8000/api/v1/dashboard/

### من الصفحة الرئيسية:
- قسم جديد: **"إعدادات الذكاء الاصطناعي"**
- أزرار: **"إعدادات"** و **"إضافة"**

## 🔑 إعداد مقدمي الخدمات

### 1. Google Gemini (مجاني)
#### الحصول على المفتاح:
1. اذهب إلى [Google AI Studio](https://makersuite.google.com/app/apikey)
2. سجل دخول بحساب Google
3. انقر "Create API Key"
4. انسخ المفتاح

#### النماذج المتاحة:
- `gemini-1.5-flash` - سريع ومتوازن (مُوصى به)
- `gemini-1.5-pro` - أداء عالي
- `gemini-pro` - النموذج الكلاسيكي

#### الإعدادات المُوصى بها:
- **الرموز**: 1000-2000
- **الإبداع**: 0.7

### 2. OpenAI GPT (مدفوع)
#### الحصول على المفتاح:
1. اذهب إلى [OpenAI Platform](https://platform.openai.com/api-keys)
2. أنشئ حساب أو سجل دخول
3. انقر "Create new secret key"
4. انسخ المفتاح فوراً

#### النماذج المتاحة:
- `gpt-3.5-turbo` - سريع واقتصادي
- `gpt-4` - أداء متقدم
- `gpt-4-turbo` - الأحدث والأسرع

#### الإعدادات المُوصى بها:
- **الرموز**: 1000-4000
- **الإبداع**: 0.7

### 3. Anthropic Claude (مدفوع)
#### الحصول على المفتاح:
1. اذهب إلى [Anthropic Console](https://console.anthropic.com/)
2. أنشئ حساب
3. أنشئ API key
4. انسخ المفتاح

#### النماذج المتاحة:
- `claude-3-sonnet` - متوازن
- `claude-3-opus` - أداء عالي
- `claude-3-haiku` - سريع

### 4. Hugging Face (مجاني/مدفوع)
#### الحصول على المفتاح:
1. اذهب إلى [Hugging Face](https://huggingface.co/settings/tokens)
2. أنشئ حساب
3. أنشئ Access Token
4. انسخ المفتاح

#### النماذج المتاحة:
- `microsoft/DialoGPT-large`
- `facebook/blenderbot-400M-distill`
- `google/flan-t5-large`

### 5. Ollama (مجاني - محلي)
#### التثبيت:
1. حمل [Ollama](https://ollama.ai/)
2. ثبته على الخادم
3. شغل نموذج: `ollama run llama2`
4. لا يحتاج مفتاح API

#### النماذج المتاحة:
- `llama2` - نموذج Meta
- `codellama` - للبرمجة
- `mistral` - نموذج سريع

## 📋 خطوات الإعداد التفصيلية

### 1. الوصول لإعدادات AI
```
http://localhost:8000/api/v1/ai/settings/
```

### 2. إضافة مقدم خدمة جديد
1. انقر "إضافة إعداد جديد"
2. اختر مقدم الخدمة
3. أدخل مفتاح API
4. اختر النموذج
5. اضبط الإعدادات المتقدمة
6. اختبر الاتصال
7. احفظ الإعداد

### 3. تعيين المقدم الافتراضي
1. اذهب لإعدادات AI
2. انقر "تعديل" على المقدم المطلوب
3. فعل "الإعداد الافتراضي"
4. احفظ التغييرات

### 4. اختبار النظام
1. اذهب لمحادثة AI
2. ابدأ محادثة جديدة
3. تأكد من عمل المقدم الافتراضي

## 🔧 الإعدادات المتقدمة

### معاملات التحكم:
- **max_tokens**: الحد الأقصى للرموز (1-8000)
- **temperature**: درجة الإبداع (0-1)
  - 0 = محافظ ومتسق
  - 1 = مبدع ومتنوع

### أمثلة للاستخدامات المختلفة:
- **المحادثة العامة**: temperature=0.7, tokens=1000
- **الكتابة الإبداعية**: temperature=0.9, tokens=2000
- **التحليل التقني**: temperature=0.3, tokens=1500
- **الترجمة**: temperature=0.2, tokens=1000

## 🛡️ الأمان والخصوصية

### حماية المفاتيح:
- ✅ تشفير المفاتيح في قاعدة البيانات
- ✅ عدم عرض المفاتيح كاملة في الواجهة
- ✅ إمكانية إخفاء/إظهار المفاتيح
- ✅ حذف آمن للمفاتيح

### أفضل الممارسات:
- 🔐 لا تشارك مفاتيح API مع أحد
- 🔄 جدد المفاتيح بانتظام
- 📊 راقب استخدام المفاتيح
- 🚫 احذف المفاتيح غير المستخدمة

## 📊 مراقبة الاستخدام

### الإحصائيات المتاحة:
- عدد المحادثات لكل مقدم
- عدد الرسائل المرسلة
- آخر استخدام
- معدل نجاح الطلبات

### التقارير:
- استخدام شهري لكل مقدم
- أكثر النماذج استخداماً
- أوقات الذروة
- معدل الأخطاء

## 🔄 التحديث والصيانة

### تحديث المقدمين:
```bash
python manage.py setup_ai_providers
```

### إضافة مقدم جديد:
1. أضف في `AIProvider.PROVIDER_CHOICES`
2. حدث `setup_ai_providers` command
3. أضف منطق الاختبار في `test_ai_config`

## 🆘 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. "مفتاح API غير صحيح"
- تأكد من صحة المفتاح
- تحقق من انتهاء صلاحية المفتاح
- تأكد من تفعيل الخدمة

#### 2. "تعذر الاتصال"
- تحقق من الاتصال بالإنترنت
- تأكد من عدم حجب الخدمة
- جرب مقدم خدمة آخر

#### 3. "تجاوز الحد المسموح"
- تحقق من رصيد الحساب
- قلل عدد الرموز المطلوبة
- انتظر قبل المحاولة مرة أخرى

## 🎯 الخطوات التالية

1. **شغل النظام**: `start_with_ai_setup.bat`
2. **أضف مفاتيح API**: اذهب لإعدادات AI
3. **اختبر الاتصالات**: تأكد من عمل المفاتيح
4. **عين المقدم الافتراضي**: للاستخدام العام
5. **ابدأ المحادثة**: جرب ميزات AI

---

## 🎉 تهانينا!

الآن لديك نظام ذكاء اصطناعي متكامل مع:
- ✅ دعم مقدمين متعددين
- ✅ إدارة آمنة للمفاتيح  
- ✅ واجهات سهلة الاستخدام
- ✅ اختبار وتحقق من الاتصالات
- ✅ مراقبة وإحصائيات
- ✅ مرونة في الإعدادات

**للبدء الآن**: `start_with_ai_setup.bat`
