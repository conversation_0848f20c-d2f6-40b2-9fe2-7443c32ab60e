{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}
{% load django_permissions %}

{% block title %}بحث الموظفين - نظام الدولية{% endblock %}

{% block page_title %}بحث الموظفين{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:list' %}">الموظفين</a></li>
<li class="breadcrumb-item active">بحث الموظفين</li>
{% endblock %}

{% block extra_css %}
<style>
    .search-card {
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .search-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }

    .employee-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .analytics-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .metric-card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card search-card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    البحث عن موظف
                </h5>
            </div>
            <div class="card-body">
                <form method="get" class="needs-validation" novalidate>
                    {% csrf_token %}
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="{{ form.employee_code.id_for_label }}" class="form-label">{{ form.employee_code.label }}</label>
                            {{ form.employee_code }}
                            {% if form.employee_code.errors %}
                            <div class="invalid-feedback d-block">{{ form.employee_code.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">{{ form.name.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-3">
                            <label for="{{ form.national_id.id_for_label }}" class="form-label">{{ form.national_id.label }}</label>
                            {{ form.national_id }}
                            {% if form.national_id.errors %}
                            <div class="invalid-feedback d-block">{{ form.national_id.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-3">
                            <label for="{{ form.insurance_number.id_for_label }}" class="form-label">{{ form.insurance_number.label }}</label>
                            {{ form.insurance_number }}
                            {% if form.insurance_number.errors %}
                            <div class="invalid-feedback d-block">{{ form.insurance_number.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="d-flex gap-2 mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                        <a href="{% url 'Hr:employees:employee_search' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-sync-alt me-1"></i>
                            مسح
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% if employees %}
    {% if selected_employee %}
        <!-- بيانات الموظف المحدد -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card search-card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            بيانات الموظف
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center mb-3">
                                {% if selected_employee.emp_image %}
                                    <img src="{{ selected_employee.emp_image|binary_to_img }}"
                                         alt="{{ selected_employee.emp_full_name }}"
                                         class="employee-avatar">
                                {% else %}
                                    <div class="employee-avatar bg-primary text-white d-flex align-items-center justify-content-center fs-1">
                                        {{ selected_employee.emp_first_name|slice:":1"|upper }}
                                    </div>
                                {% endif %}
                                <h6 class="mt-2 mb-0">{{ selected_employee.emp_full_name }}</h6>
                                <small class="text-muted">كود: {{ selected_employee.emp_id }}</small>
                            </div>
                            <div class="col-md-9">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <strong>الاسم الأول:</strong> {{ selected_employee.emp_first_name|default:"-" }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>الاسم الثاني:</strong> {{ selected_employee.emp_second_name|default:"-" }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>الرقم القومي:</strong> {{ selected_employee.national_id|default:"-" }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>رقم الهاتف:</strong> {{ selected_employee.emp_phone1|default:"-" }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>القسم:</strong>
                                        {% if selected_employee.department %}
                                            <a href="{% url 'Hr:departments:detail' selected_employee.department.dept_code %}" class="text-decoration-none">
                                                {{ selected_employee.department.dept_name }}
                                            </a>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>الوظيفة:</strong> {{ selected_employee.jop_name|default:"-" }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>حالة العمل:</strong>
                                        {% if selected_employee.working_condition == 'سارى' %}
                                            <span class="badge bg-success">{{ selected_employee.working_condition }}</span>
                                        {% elif selected_employee.working_condition == 'استقالة' %}
                                            <span class="badge bg-danger">{{ selected_employee.working_condition }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ selected_employee.working_condition|default:"-" }}</span>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>تاريخ التعيين:</strong> {{ selected_employee.emp_date_hiring|date:"Y-m-d"|default:"-" }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>حالة التأمين:</strong> {{ selected_employee.insurance_status|default:"-" }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>العنوان:</strong> {{ selected_employee.emp_address|default:"-" }}
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="{% url 'Hr:employees:detail' selected_employee.emp_id %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض التفاصيل الكاملة
                                    </a>
                                    <a href="{% url 'Hr:employees:edit' selected_employee.emp_id %}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card analytics-card">
                    <div class="card-header border-0">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            تحليلات الأداء
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="metric-card mb-3">
                            <h3 class="mb-1">{{ analytics.attendance_rate|default:"95" }}%</h3>
                            <small>نسبة الحضور</small>
                        </div>
                        <div class="metric-card mb-3">
                            <h3 class="mb-1">{{ analytics.task_completion_rate|default:"90" }}%</h3>
                            <small>نسبة إتمام المهام</small>
                        </div>
                        <div class="metric-card">
                            <h3 class="mb-1">{{ analytics.evaluation_score|default:"4.5" }}/5</h3>
                            <small>تقييم الأداء</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <!-- نتائج البحث المتعددة -->
        <div class="row">
            <div class="col-12">
                <div class="card search-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            نتائج البحث ({{ employees.count }} موظف)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>الصورة</th>
                                        <th>الاسم</th>
                                        <th>الكود</th>
                                        <th>القسم</th>
                                        <th>الوظيفة</th>
                                        <th>الحالة</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for emp in employees %}
                                    <tr>
                                        <td>
                                            {% if emp.emp_image %}
                                                <img src="{{ emp.emp_image|binary_to_img }}"
                                                     alt="{{ emp.emp_full_name }}"
                                                     class="rounded-circle"
                                                     width="40" height="40">
                                            {% else %}
                                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 40px; height: 40px;">
                                                    {{ emp.emp_first_name|slice:":1"|upper }}
                                                </div>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <strong>{{ emp.emp_full_name }}</strong><br>
                                            <small class="text-muted">{{ emp.national_id|default:"-" }}</small>
                                        </td>
                                        <td><span class="badge bg-light text-dark">{{ emp.emp_id }}</span></td>
                                        <td>{{ emp.department.dept_name|default:"-" }}</td>
                                        <td>{{ emp.jop_name|default:"-" }}</td>
                                        <td>
                                            {% if emp.working_condition == 'سارى' %}
                                                <span class="badge bg-success">نشط</span>
                                            {% elif emp.working_condition == 'استقالة' %}
                                                <span class="badge bg-danger">مستقيل</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ emp.working_condition|default:"-" }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="?employee_code={{ emp.emp_id }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'Hr:employees:detail' emp.emp_id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-info-circle"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
{% elif request.GET %}
    <!-- لا توجد نتائج -->
    <div class="row">
        <div class="col-12">
            <div class="card search-card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد نتائج للبحث</h5>
                    <p class="text-muted">جرب تغيير معايير البحث أو تأكد من صحة البيانات المدخلة</p>
                </div>
            </div>
        </div>
    </div>
{% endif %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحسين تجربة البحث
        const searchForm = document.querySelector('form');
        const searchInputs = searchForm.querySelectorAll('input[type="text"]');

        // البحث التلقائي عند الكتابة (مع تأخير)
        let searchTimeout;
        searchInputs.forEach(input => {
            input.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 3) {
                        // يمكن إضافة AJAX search هنا
                        console.log('البحث عن:', this.value);
                    }
                }, 500);
            });
        });

        // تحسين عرض النتائج
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('click', function(e) {
                if (!e.target.closest('a')) {
                    const viewButton = this.querySelector('a[href*="employee_code"]');
                    if (viewButton) {
                        window.location.href = viewButton.href;
                    }
                }
            });
        });
    });
</script>
{% endblock %}
{% endblock %}
