{% extends 'tasks/base_tasks.html' %}
{% load static %}

{% block title %}قائمة المهام - نظام الدولية{% endblock %}

{% block page_title %}قائمة المهام{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">المهام</li>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h5 class="mb-1">المهام المسندة إليك ({{ tasks|length }})</h5>
        <p class="text-muted mb-0">عرض ومتابعة المهام المسندة إليك</p>
    </div>
</div>

<!-- Task Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">بحث</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" placeholder="البحث في وصف المهمة..." value="{{ request.GET.search|default:'' }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="" {% if not request.GET.status %}selected{% endif %}>جميع الحالات</option>
                    <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                    <option value="in_progress" {% if request.GET.status == 'in_progress' %}selected{% endif %}>يجرى العمل عليها</option>
                    <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>مكتملة</option>
                    <option value="deferred" {% if request.GET.status == 'deferred' %}selected{% endif %}>مؤجلة</option>
                    <option value="canceled" {% if request.GET.status == 'canceled' %}selected{% endif %}>ملغاة</option>
                    <option value="failed" {% if request.GET.status == 'failed' %}selected{% endif %}>فشلت</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="sort" class="form-label">الترتيب حسب</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="start_date" {% if request.GET.sort == 'start_date' or not request.GET.sort %}selected{% endif %}>تاريخ البدء</option>
                    <option value="end_date" {% if request.GET.sort == 'end_date' %}selected{% endif %}>تاريخ الانتهاء</option>
                    <option value="status" {% if request.GET.sort == 'status' %}selected{% endif %}>الحالة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label d-block">&nbsp;</label>
                <button type="button" class="btn btn-outline-secondary w-100" id="resetFilters">
                    <i class="fas fa-redo-alt me-1"></i>
                    إعادة ضبط
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Task Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card light">
            <i class="fas fa-list-alt stats-icon"></i>
            <div class="stats-number">{{ total_count|default:"0" }}</div>
            <p class="stats-title">إجمالي المهام</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card primary">
            <i class="fas fa-hourglass-half stats-icon"></i>
            <div class="stats-number">{{ in_progress_count|default:"0" }}</div>
            <p class="stats-title">قيد التنفيذ</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card success">
            <i class="fas fa-check-circle stats-icon"></i>
            <div class="stats-number">{{ completed_count|default:"0" }}</div>
            <p class="stats-title">مكتملة</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card secondary">
            <i class="fas fa-calendar-day stats-icon"></i>
            <div class="stats-number">{{ overdue_count|default:"0" }}</div>
            <p class="stats-title">متأخرة</p>
        </div>
    </div>
</div>

<!-- Task List -->
<div class="task-list">
    {% if tasks %}
        {% for task in tasks %}
        <div class="card task-card mb-3 {{ task.status }}">
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-9 mb-3 mb-lg-0">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge {% if task.status == 'pending' %}bg-secondary{% elif task.status == 'in_progress' %}bg-primary{% elif task.status == 'completed' %}bg-success{% elif task.status == 'canceled' %}bg-danger{% elif task.status == 'deferred' %}bg-warning{% else %}bg-info{% endif %} me-2">
                                {{ task.get_status_display }}
                            </span>
                            {% if task.meeting %}
                            <a href="{% url 'meetings:detail' task.meeting.id %}" class="text-decoration-none ms-2 small">
                                <i class="fas fa-link me-1"></i>
                                {{ task.meeting.title }}
                            </a>
                            {% endif %}
                        </div>

                        <h5 class="card-title mb-3">{{ task.description|truncatechars:100 }}</h5>

                        <div class="d-flex flex-wrap task-meta">
                            <div class="me-4 mb-2">
                                <i class="fas fa-calendar-day text-primary me-1"></i>
                                <span class="small text-muted">البدء:</span>
                                <span class="small">{{ task.start_date|date:"j F Y g:i A" }}</span>
                            </div>
                            <div class="me-4 mb-2">
                                <i class="fas fa-calendar-check text-danger me-1"></i>
                                <span class="small text-muted">الإنتهاء:</span>
                                <span class="small">{{ task.end_date|date:"j F Y g:i A" }}</span>
                            </div>
                            <div class="mb-2">
                                <i class="fas fa-tasks text-success me-1"></i>
                                <span class="small text-muted">الخطوات:</span>
                                <span class="small">{{ task.steps.count }}</span>
                            </div>
                        </div>

                        {% if task.status == 'in_progress' %}
                        <div class="progress mt-3" style="height: 5px;">
                            <!-- Show a simple progress bar for in-progress tasks -->
                            <div class="progress-bar bg-primary" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-lg-3">
                        <div class="d-flex flex-column h-100 justify-content-between">
                            <div class="mb-3">
                                <span class="d-block mb-1 small text-muted">المكلف</span>
                                <span class="d-flex align-items-center">
                                    <div class="avatar-circle bg-primary text-white me-2">
                                        {{ task.assigned_to.username|slice:":1" }}
                                    </div>
                                    {{ task.assigned_to.username }}
                                </span>
                            </div>
                            <div class="d-grid gap-2">
                                <a href="{% url 'tasks:detail' task.id %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض التفاصيل
                                </a>
                                {% if task.status != 'completed' %}
                                <button class="btn btn-outline-success btn-sm change-status" data-task-id="{{ task.id }}" data-new-status="completed">
                                    <i class="fas fa-check me-1"></i>
                                    تحديد كمكتملة
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-clipboard-list text-muted fa-4x mb-3"></i>
            <h5>لا توجد مهام</h5>
            <p class="text-muted">{% if request.GET.search or request.GET.status %}لا توجد مهام تطابق معايير البحث{% else %}ليس لديك أي مهام مسندة إليك حالياً{% endif %}</p>
        </div>
    {% endif %}
</div>

<!-- Pagination -->
{% if tasks.has_other_pages %}
<nav aria-label="Page navigation" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if tasks.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page={{ tasks.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
        {% else %}
        <li class="page-item disabled">
            <span class="page-link">&laquo;</span>
        </li>
        {% endif %}

        {% for i in tasks.paginator.page_range %}
            {% if tasks.number == i %}
            <li class="page-item active"><span class="page-link">{{ i }}</span></li>
            {% else %}
            <li class="page-item">
                <a class="page-link" href="?page={{ i }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">{{ i }}</a>
            </li>
            {% endif %}
        {% endfor %}

        {% if tasks.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ tasks.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
        {% else %}
        <li class="page-item disabled">
            <span class="page-link">&raquo;</span>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<style>
    .avatar-circle {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .task-card {
        transition: all 0.3s ease;
    }

    .task-card.pending {
        border-right: 5px solid var(--pending);
    }

    .task-card.in_progress {
        border-right: 5px solid var(--in-progress);
    }

    .task-card.completed {
        border-right: 5px solid var(--completed);
    }

    .task-card.canceled {
        border-right: 5px solid var(--canceled);
    }

    .task-card.deferred {
        border-right: 5px solid var(--deferred);
    }

    .task-card.failed {
        border-right: 5px solid var(--failed);
    }

    .task-meta {
        color: var(--medium);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Reset filters button
        document.getElementById('resetFilters').addEventListener('click', function() {
            window.location.href = "{% url 'tasks:list' %}";
        });

        // Form submit on select change
        document.getElementById('status').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('sort').addEventListener('change', function() {
            this.form.submit();
        });

        // Quick complete task buttons
        const completeButtons = document.querySelectorAll('.change-status');
        completeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const taskId = this.dataset.taskId;
                const newStatus = this.dataset.newStatus;
                const csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]').value;

                // Show loading state
                this.disabled = true;
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';

                fetch(`/tasks/${taskId}/update_status/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify({status: newStatus})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload page to show updated task
                        window.location.reload();
                    } else {
                        alert('حدث خطأ أثناء تحديث الحالة');
                        // Restore button state
                        this.disabled = false;
                        this.innerHTML = originalText;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء الاتصال بالخادم');
                    // Restore button state
                    this.disabled = false;
                    this.innerHTML = originalText;
                });
            });
        });
    });
</script>
{% endblock %}
