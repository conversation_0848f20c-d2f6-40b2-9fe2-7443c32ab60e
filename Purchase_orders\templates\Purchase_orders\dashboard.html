{% extends 'Purchase_orders/base_purchase.html' %}
{% load static %}
{% load django_permissions %}

{% block title %}لوحة تحكم طلبات الشراء - نظام الدولية{% endblock %}

{% block page_title %}لوحة تحكم طلبات الشراء{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item active">لوحة تحكم طلبات الشراء</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2 class="mb-3">
            <i class="fas fa-tachometer-alt me-2"></i>لوحة تحكم طلبات الشراء
        </h2>
        <p class="text-muted">مرحبًا بك في نظام إدارة طلبات الشراء. يمكنك من هنا متابعة طلبات الشراء وإدارتها.</p>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h5 class="card-title">إجمالي الطلبات</h5>
                <h2 class="display-4">{{ total_requests }}</h2>
                <p class="card-text">
                    <i class="fas fa-shopping-cart"></i> العدد الكلي للطلبات
                </p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <h5 class="card-title">قيد الانتظار</h5>
                <h2 class="display-4">{{ pending_requests }}</h2>
                <p class="card-text">
                    <i class="fas fa-clock"></i> طلبات تنتظر الموافقة
                </p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h5 class="card-title">تمت الموافقة</h5>
                <h2 class="display-4">{{ approved_requests }}</h2>
                <p class="card-text">
                    <i class="fas fa-check-circle"></i> طلبات تمت الموافقة عليها
                </p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <h5 class="card-title">مرفوضة</h5>
                <h2 class="display-4">{{ rejected_requests }}</h2>
                <p class="card-text">
                    <i class="fas fa-times-circle"></i> طلبات تم رفضها
                </p>
            </div>
        </div>
    </div>
</div>

<!-- أحدث طلبات الشراء -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>أحدث طلبات الشراء
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>تاريخ الطلب</th>
                                <th>مقدم الطلب</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in recent_requests %}
                            <tr>
                                <td>{{ request.request_number }}</td>
                                <td>{{ request.request_date|date:"Y-m-d H:i" }}</td>
                                <td>{{ request.requested_by.get_full_name|default:request.requested_by.username }}</td>
                                <td>
                                    {% if request.status == 'pending' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif request.status == 'approved' %}
                                        <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif request.status == 'rejected' %}
                                        <span class="badge bg-danger">مرفوض</span>
                                    {% elif request.status == 'completed' %}
                                        <span class="badge bg-info">مكتمل</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'Purchase_orders:purchase_request_detail' pk=request.pk %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center">لا توجد طلبات شراء حتى الآن</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-end mt-3">
                    {% if perms.Purchase_orders.view_purchaserequest or user|is_admin %}
                        <a href="{% url 'Purchase_orders:purchase_request_list' %}" class="btn btn-primary">
                            <i class="fas fa-list me-1"></i>عرض جميع الطلبات
                        </a>
                    {% endif %}
                    {% if perms.Purchase_orders.add_purchaserequest or user|is_admin %}
                        <a href="{% url 'Purchase_orders:create_purchase_request' %}" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>إنشاء طلب جديد
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
