<!-- templates/inventory/customer_list.html - Simplified -->
{% extends 'inventory/base_inventory.html' %}

{% block title %}قائمة العملاء - نظام إدارة المخزن{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h3>قائمة العملاء</h3>
                {% if perms.inventory.add_customer or user.is_superuser %}
                <a href="{% url 'inventory:customer_add' %}" class="btn btn-success">
                    <i class="fas fa-plus-circle me-1"></i> إضافة عميل جديد
                </a>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="input-group">
                <input type="text" id="searchInput" class="form-control" placeholder="ابحث عن عميل...">
                <div class="input-group-append">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">جميع العملاء</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم العميل</th>
                                    <th>رقم الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in customers %}
                                <tr>
                                    <td>{{ customer.id }}</td>
                                    <td>{{ customer.name }}</td>
                                    <td>{{ customer.phone|default:"-" }}</td>
                                    <td>{{ customer.email|default:"-" }}</td>
                                    <td>
                                        {% if perms.inventory.change_customer or user.is_superuser %}
                                        <a href="{% url 'inventory:customer_edit' customer.id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        {% endif %}
                                        
                                        {% if perms.inventory.delete_customer or user.is_superuser %}
                                        <a href="{% url 'inventory:customer_delete' customer.id %}" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">
                                        <p>لا يوجد عملاء حالياً</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    {% if perms.inventory.add_customer or user.is_superuser %}
                    <a href="{% url 'inventory:customer_add' %}" class="btn btn-success">
                        <i class="fas fa-plus-circle"></i> إضافة عميل جديد
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchText = this.value.toLowerCase();
                const tableRows = document.querySelectorAll('tbody tr');
                
                tableRows.forEach(function(row) {
                    const nameCell = row.querySelector('td:nth-child(2)');
                    const phoneCell = row.querySelector('td:nth-child(3)');
                    const emailCell = row.querySelector('td:nth-child(4)');
                    
                    if (!nameCell) return; // Skip if not a data row
                    
                    const name = nameCell.textContent.toLowerCase();
                    const phone = phoneCell.textContent.toLowerCase();
                    const email = emailCell.textContent.toLowerCase();
                    
                    if (name.includes(searchText) || 
                        phone.includes(searchText) || 
                        email.includes(searchText)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }
    });
</script>
{% endblock %}
