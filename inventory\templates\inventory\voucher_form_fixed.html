{% extends "inventory/base_inventory.html" %}
{% load static %}
{% load i18n %}

{% block extra_css %}
<style>
    /* مؤشر التحميل للصفوف */
    tr.item-row.loading {
        position: relative;
        opacity: 0.7;
    }

    tr.item-row.loading::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.7) url('{% static "inventory/img/loading.gif" %}') no-repeat center center;
        background-size: 20px;
        z-index: 1;
    }

    /* تنسيق حقول الإدخال مع الأزرار */
    .product-code-container {
        position: relative;
    }

    .search-product-btn, .barcode-scan-btn {
        position: absolute;
        top: 5px;
        z-index: 2;
    }

    .search-product-btn {
        right: 5px;
    }

    .barcode-scan-btn {
        right: 40px;
    }

    /* تنسيق رسائل الخطأ */
    .row-error-message {
        font-size: 0.8rem;
        color: #dc3545;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block title %}
{% if form.instance.voucher_number %}
    {% trans "تعديل إذن" %}: {{ form.instance.voucher_number }}
{% else %}
    {% if voucher_type == 'إذن اضافة' %}
        {% trans "إضافة إذن اضافة جديد" %}
    {% elif voucher_type == 'إذن صرف' %}
        {% trans "إضافة إذن صرف جديد" %}
    {% elif voucher_type == 'اذن مرتجع عميل' %}
        {% trans "إضافة إذن مرتجع عميل جديد" %}
    {% elif voucher_type == 'إذن مرتجع مورد' %}
        {% trans "إضافة إذن مرتجع مورد جديد" %}
    {% else %}
        {% trans "إضافة إذن جديد" %}
    {% endif %}
{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- العنوان والأزرار -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        {% if form.instance.voucher_number %}
            <h1 class="h3 mb-0 text-gray-800">{% trans "تعديل إذن" %}: {{ form.instance.voucher_number }}</h1>
        {% else %}
            {% if voucher_type == 'إذن اضافة' %}
                <h1 class="h3 mb-0 text-gray-800">{% trans "إذن اضافة جديد" %}</h1>
            {% elif voucher_type == 'إذن صرف' %}
                <h1 class="h3 mb-0 text-gray-800">{% trans "إذن صرف جديد" %}</h1>
            {% elif voucher_type == 'اذن مرتجع عميل' %}
                <h1 class="h3 mb-0 text-gray-800">{% trans "إذن مرتجع عميل جديد" %}</h1>
            {% elif voucher_type == 'إذن مرتجع مورد' %}
                <h1 class="h3 mb-0 text-gray-800">{% trans "إذن مرتجع مورد جديد" %}</h1>
            {% else %}
                <h1 class="h3 mb-0 text-gray-800">{% trans "إذن جديد" %}</h1>
            {% endif %}
        {% endif %}
        <div>
            <a href="{% url 'inventory:voucher_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> {% trans "العودة للقائمة" %}
            </a>
        </div>
    </div>

    <!-- نموذج الإذن -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "بيانات الإذن" %}</h6>
        </div>
        <div class="card-body">
            <form method="post" id="voucher-form">
                {% csrf_token %}
                {% if form.errors %}
                <div class="alert alert-danger">
                    {% for field in form %}
                        {% for error in field.errors %}
                            <p>{{ field.label }}: {{ error }}</p>
                        {% endfor %}
                    {% endfor %}
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
                {% endif %}

                <input type="hidden" name="voucher_type" id="id_voucher_type" value="{% if voucher_type %}{{ voucher_type }}{% else %}{{ form.instance.voucher_type }}{% endif %}">

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="id_voucher_number">{% trans "رقم الإذن" %}*</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="id_voucher_number" name="voucher_number" value="{{ form.instance.voucher_number|default_if_none:'' }}" required>
                                {% if not form.instance.voucher_number %}
                                <button type="button" id="generate-number-btn" class="btn btn-outline-secondary">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="id_date">{% trans "تاريخ الإذن" %}*</label>
                            <input type="date" class="form-control" id="id_date" name="date" value="{{ form.instance.date|date:'Y-m-d'|default:today }}" required>
                        </div>
                    </div>

                    {% if voucher_type == 'إذن اضافة' or form.instance.voucher_type == 'إذن اضافة' %}
                    <!-- حقول إذن الإضافة -->
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="id_supplier">{% trans "المورد" %}*</label>
                            <div class="input-group">
                                <select class="form-select" id="id_supplier" name="supplier" required>
                                    <option value="">---------</option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}" {% if supplier.id == form.instance.supplier_id %}selected{% endif %}>{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                                <a href="{% url 'inventory:supplier_add' %}?next={{ request.path }}" class="btn btn-outline-secondary" target="_blank">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="id_supplier_voucher_number">{% trans "رقم الإذن للمورد" %}*</label>
                            <input type="text" class="form-control" id="id_supplier_voucher_number" name="supplier_voucher_number" value="{{ form.instance.supplier_voucher_number|default_if_none:'' }}" required>
                        </div>
                    </div>
                    {% endif %}

                    {% if voucher_type == 'إذن صرف' or form.instance.voucher_type == 'إذن صرف' %}
                    <!-- حقول إذن الصرف -->
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="id_department">{% trans "القسم" %}*</label>
                            <div class="input-group">
                                <select class="form-select" id="id_department" name="department" required>
                                    <option value="">---------</option>
                                    {% for department in departments %}
                                    <option value="{{ department.id }}" {% if department.id == form.instance.department_id %}selected{% endif %}>{{ department.name }}</option>
                                    {% endfor %}
                                </select>
                                <a href="{% url 'inventory:department_add' %}?next={{ request.path }}" class="btn btn-outline-secondary" target="_blank">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="id_recipient">{% trans "المستلم" %}*</label>
                            <input type="text" class="form-control" id="id_recipient" name="recipient" value="{{ form.instance.recipient|default_if_none:'' }}" required>
                        </div>
                    </div>
                    {% endif %}

                    {% if voucher_type == 'اذن مرتجع عميل' or form.instance.voucher_type == 'اذن مرتجع عميل' %}
                    <!-- حقول إذن مرتجع عميل -->
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="id_customer">{% trans "العميل" %}*</label>
                            <div class="input-group">
                                <select class="form-select" id="id_customer" name="customer" required>
                                    <option value="">---------</option>
                                    {% for customer in customers %}
                                    <option value="{{ customer.id }}" {% if customer.id == form.instance.customer_id %}selected{% endif %}>{{ customer.name }}</option>
                                    {% endfor %}
                                </select>
                                <a href="{% url 'inventory:customer_add' %}?next={{ request.path }}" class="btn btn-outline-secondary" target="_blank">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="id_return_voucher_number">{% trans "رقم إذن المرتجع" %}*</label>
                            <input type="text" class="form-control" id="id_return_voucher_number" name="return_voucher_number" value="{{ form.instance.return_voucher_number|default_if_none:'' }}" required>
                        </div>
                    </div>
                    {% endif %}

                    {% if voucher_type == 'إذن مرتجع مورد' or form.instance.voucher_type == 'إذن مرتجع مورد' %}
                    <!-- حقول إذن مرتجع مورد -->
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="id_supplier">{% trans "المورد" %}*</label>
                            <div class="input-group">
                                <select class="form-select" id="id_supplier" name="supplier" required>
                                    <option value="">---------</option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}" {% if supplier.id == form.instance.supplier_id %}selected{% endif %}>{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                                <a href="{% url 'inventory:supplier_add' %}?next={{ request.path }}" class="btn btn-outline-secondary" target="_blank">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="id_supplier_voucher_number">{% trans "رقم الإذن للمورد" %}*</label>
                            <input type="text" class="form-control" id="id_supplier_voucher_number" name="supplier_voucher_number" value="{{ form.instance.supplier_voucher_number|default_if_none:'' }}" required>
                        </div>
                    </div>
                    {% endif %}

                    <!-- حقول مشتركة -->
                    <div class="col-md-12 mb-3">
                        <div class="form-group">
                            <label for="id_notes">{% trans "ملاحظات" %}</label>
                            <textarea class="form-control" id="id_notes" name="notes" rows="3">{{ form.instance.notes|default_if_none:'' }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- جدول الأصناف -->
                <div class="card mt-4">
                    <div class="card-header py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">{% trans "الأصناف" %}</h6>
                            <button type="button" id="add-item-btn" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> {% trans "إضافة صنف" %}
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="items-table">
                                <thead>
                                    <tr>
                                        <th width="15%">
                                            {% trans "كود الصنف" %}
                                            <i class="fas fa-info-circle text-info" data-bs-toggle="tooltip" title="يمكنك البحث عن الصنف بالكود أو استخدام زر البحث للبحث بالاسم أو التصنيف"></i>
                                        </th>
                                        <th width="20%">{% trans "اسم الصنف" %}</th>
                                        <th width="10%">{% trans "الرصيد الحالي" %}</th>
                                        <th width="15%">{% trans "الوحدة" %}</th>

                                        {% if voucher_type == 'إذن اضافة' or form.instance.voucher_type == 'إذن اضافة' or voucher_type == 'اذن مرتجع عميل' or form.instance.voucher_type == 'اذن مرتجع عميل' %}
                                        <th width="10%">{% trans "الكمية المضافة" %}*</th>
                                        {% else %}
                                        <th width="10%">{% trans "الكمية المنصرفة" %}*</th>
                                        {% endif %}

                                        {% if voucher_type == 'إذن صرف' or form.instance.voucher_type == 'إذن صرف' %}
                                        <th width="15%">{% trans "الماكينة" %}</th>
                                        <th width="15%">{% trans "وحدة الماكينة" %}</th>
                                        {% endif %}

                                        <th width="5%"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if voucher_items %}
                                        <!-- Hidden management form fields for Django formset -->
                                        <input type="hidden" name="form-TOTAL_FORMS" value="{{ voucher_items|length }}">
                                        <input type="hidden" name="form-INITIAL_FORMS" value="{{ voucher_items|length }}">
                                        <input type="hidden" name="form-MIN_NUM_FORMS" value="0">
                                        <input type="hidden" name="form-MAX_NUM_FORMS" value="1000">

                                        {% for item in voucher_items %}
                                            <tr class="item-row">
                                                <td>
                                                    <div class="product-code-container">
                                                        <input type="text" class="form-control product-code" name="form-{{ forloop.counter0 }}-product_code" value="{{ item.product.product_id }}" required>
                                                        <input type="hidden" class="product-id" name="form-{{ forloop.counter0 }}-product" value="{{ item.product.product_id }}">
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="product-name">{{ item.product.name }}</span>
                                                </td>
                                                <td>
                                                    <span class="current-stock">{{ item.product.quantity }}</span>
                                                </td>
                                                <td>
                                                    <span class="unit-name">{{ item.product.unit.name|default_if_none:'' }}</span>
                                                </td>

                                                {% if form.instance.voucher_type == 'إذن اضافة' or form.instance.voucher_type == 'اذن مرتجع عميل' %}
                                                <td>
                                                    <input type="number" class="form-control quantity" name="form-{{ forloop.counter0 }}-quantity" value="{{ item.quantity_added|default_if_none:'0' }}" min="0.01" step="0.01" required>
                                                </td>
                                                {% else %}
                                                <td>
                                                    <input type="number" class="form-control quantity" name="form-{{ forloop.counter0 }}-quantity" value="{{ item.quantity_disbursed|default_if_none:'0' }}" min="0.01" step="0.01" max="{{ item.product.quantity|default_if_none:'0' }}" required>
                                                </td>
                                                {% endif %}

                                                {% if form.instance.voucher_type == 'إذن صرف' %}
                                                <td>
                                                    <input type="text" class="form-control machine-name" name="form-{{ forloop.counter0 }}-machine_name" value="{{ item.machine|default_if_none:'' }}">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control machine-unit" name="form-{{ forloop.counter0 }}-machine_unit" value="{{ item.machine_unit|default_if_none:'' }}">
                                                </td>
                                                {% endif %}

                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger delete-row">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <!-- Hidden management form fields for Django formset -->
                                        <input type="hidden" name="form-TOTAL_FORMS" value="1">
                                        <input type="hidden" name="form-INITIAL_FORMS" value="0">
                                        <input type="hidden" name="form-MIN_NUM_FORMS" value="0">
                                        <input type="hidden" name="form-MAX_NUM_FORMS" value="1000">

                                        <tr class="item-row">
                                            <td>
                                                <div class="product-code-container">
                                                    <input type="text" class="form-control product-code" name="form-0-product_code" value="" required>
                                                    <input type="hidden" class="product-id" name="form-0-product" value="">
                                                </div>
                                            </td>
                                            <td>
                                                <span class="product-name"></span>
                                            </td>
                                            <td>
                                                <span class="current-stock">0</span>
                                            </td>
                                            <td>
                                                <span class="unit-name"></span>
                                            </td>

                                            {% if voucher_type == 'إذن اضافة' or form.instance.voucher_type == 'إذن اضافة' or voucher_type == 'اذن مرتجع عميل' or form.instance.voucher_type == 'اذن مرتجع عميل' %}
                                            <td>
                                                <input type="number" class="form-control quantity" name="form-0-quantity" value="0" min="0.01" step="0.01" required>
                                            </td>
                                            {% else %}
                                            <td>
                                                <input type="number" class="form-control quantity" name="form-0-quantity" value="0" min="0.01" step="0.01" max="0" required>
                                            </td>
                                            {% endif %}

                                            {% if voucher_type == 'إذن صرف' or form.instance.voucher_type == 'إذن صرف' %}
                                            <td>
                                                <input type="text" class="form-control machine-name" name="form-0-machine_name" value="">
                                            </td>
                                            <td>
                                                <input type="text" class="form-control machine-unit" name="form-0-machine_unit" value="">
                                            </td>
                                            {% endif %}

                                            <td>
                                                <button type="button" class="btn btn-sm btn-danger delete-row">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- زر الحفظ -->
                <div class="mt-4 text-center">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> {% trans "حفظ الإذن" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- نافذة بحث المنتجات -->
<div class="modal fade" id="productSearchModal" tabindex="-1" aria-labelledby="productSearchModalLabel" aria-hidden="true" dir="rtl">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="productSearchModalLabel">
                    <i class="fas fa-search me-2"></i>بحث متقدم عن الأصناف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- قسم البحث والفلترة -->
                <div class="search-section mb-3 p-3 bg-light rounded">
                    <div class="row g-2">
                        <!-- بحث بالاسم أو الكود -->
                        <div class="col-md-12 mb-2">
                            <div class="input-group">
                                <span class="input-group-text bg-white">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" id="modal-search-input" class="form-control" placeholder="البحث بالاسم أو الرقم أو التصنيف..." autocomplete="off">
                                <button type="button" id="modal-search-btn" class="btn btn-primary">
                                    بحث
                                </button>
                                <button type="button" id="modal-barcode-btn" class="btn btn-secondary">
                                    <i class="fas fa-barcode me-1"></i>مسح الباركود
                                </button>
                            </div>
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                يمكنك البحث عن طريق كود الصنف، اسم الصنف، التصنيف أو الوحدة
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="row g-2">
                                <!-- فلتر التصنيف -->
                                <div class="col-md-4">
                                    <label for="modal-category-filter" class="form-label mb-1">التصنيف</label>
                                    <select id="modal-category-filter" class="form-select">
                                        <option value="">كل التصنيفات</option>
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </select>
                                </div>

                                <!-- فلتر الوحدة -->
                                <div class="col-md-4">
                                    <label for="modal-unit-filter" class="form-label mb-1">الوحدة</label>
                                    <select id="modal-unit-filter" class="form-select">
                                        <option value="">كل الوحدات</option>
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </select>
                                </div>

                                <!-- فلتر حالة المخزون -->
                                <div class="col-md-4">
                                    <label for="modal-stock-filter" class="form-label mb-1">حالة المخزون</label>
                                    <select id="modal-stock-filter" class="form-select">
                                        <option value="">كل الحالات</option>
                                        <option value="available">متوفر</option>
                                        <option value="out">نفذ</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="table-responsive">
                    <table class="table table-hover table-striped border" id="modal-products-table">
                        <thead class="table-light">
                            <tr>
                                <th>رقم الصنف</th>
                                <th>اسم الصنف</th>
                                <th>التصنيف</th>
                                <th>الرصيد الحالي</th>
                                <th>وحدة القياس</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملؤها ديناميكياً -->
                        </tbody>
                    </table>
                </div>

                <!-- رسالة عند عدم وجود نتائج -->
                <div id="no-products-message" class="text-center py-5 d-none">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h6 class="mb-2">لم يتم العثور على أصناف مطابقة</h6>
                    <p class="text-muted mb-0">جرب تغيير كلمات البحث أو إزالة الفلاتر</p>
                </div>

                <!-- مؤشر التحميل -->
                <div id="loading-indicator" class="text-center py-5 d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-3 mb-0">جاري تحميل الأصناف...</p>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <div>
                    <button type="button" id="modal-reset-filters" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-1"></i>إعادة ضبط الفلاتر
                    </button>
                </div>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة قارئ الباركود -->
<div class="modal fade" id="barcodeScannerModal" tabindex="-1" aria-labelledby="barcodeScannerModalLabel" aria-hidden="true" dir="rtl">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="barcodeScannerModalLabel">
                    <i class="fas fa-barcode me-2"></i>مسح الباركود
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- قسم قارئ الباركود -->
                <div id="barcode-scanner-container">
                    <div class="text-center py-3">
                        <div id="barcode-scanner-preview" class="mx-auto mb-3" style="width: 100%; max-width: 400px; height: 300px; border: 1px solid #ddd; background-color: #f8f9fa; position: relative;">
                            <video id="barcode-scanner-video" style="width: 100%; height: 100%; object-fit: cover;"></video>
                            <div id="barcode-scanner-loading" class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-light bg-opacity-75">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                            </div>
                        </div>
                        <p class="text-muted mb-2">ضع الباركود في منتصف الكاميرا للمسح التلقائي</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'inventory/js/voucher_form.js' %}"></script>
{% endblock %}